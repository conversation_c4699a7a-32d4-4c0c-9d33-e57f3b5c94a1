<template>
  <div class="mobile-container">
    <seo-data :pageData="pageData"></seo-data>
    <div class="!bg-white pt-[8px]">
      <n-marquee auto-fill>
        <div class="flex items-center gap-[6px] text-[14px] leading-[14px]">
          <img
            src="@/assets/icons/home/<USER>"
            alt=""
            loading="lazy"
            class="w-[22px] h-[22px] mr-[2px] ml-[8px]"
          />
          <span class="text-[#333] font-medium"> Mínimo desde US$ 2,000 </span>
          <span class="text-[#999]">hasta contenedor cerrado</span>
          <span class="text-[#666]"
            >hace compras directamente del mercado mayorista chino</span
          >
        </div>
      </n-marquee>
    </div>

    <!-- 头部信息 -->
    <mobile-search-bar></mobile-search-bar>

    <!-- 轮播 -->
    <div class="px-[4px] bg-white">
      <n-carousel
        autoplay
        :transition-style="{ transitionDuration: '500ms' }"
        class="home-header-carousel"
      >
        <img
          loading="lazy"
          class="w-full rounded-[8px] overflow-hidden"
          :src="carousel"
          v-for="(carousel, index) in carouselData"
          :key="index"
          :img-props="{ referrerpolicy: 'no-referrer' }"
        />
      </n-carousel>
    </div>

    <!-- 福利明细 -->
    <div class="w-full pt-[24px] bg-white">
      <div
        class="w-[335px] mx-auto h-[30px] rounded-tl-[20px] rounded-tr-[20px] bg-[#F2F3F5] flex justify-center gap-[4px]"
      >
        <img
          alt="d"
          loading="lazy"
          class="w-[30px] flex-shrink-0 mt-[-8px]"
          src="@/assets/icons/home/<USER>"
        />
        <img
          loading="lazy"
          class="w-[224px] flex-shrink-0"
          alt="esglose de beneficios"
          src="@/assets/icons/home/<USER>"
        />
      </div>
      <div class="relative w-full benefit-breakdown overflow-hidden">
        <img
          alt="bg"
          loading="lazy"
          class="absolute top-[16px] right-[-13px] w-[140px]"
          src="@/assets/icons/home/<USER>"
        />
        <div
          class="hidden-scrollbar pt-[22px] pb-[18px] pl-[4px] pr-[12px] flex gap-[1px] relative z-2 overflow-x-auto"
        >
          <div
            v-for="(item, index) in benefitBreakdown"
            :key="index"
            class="flex flex-col items-center gap-[8px]"
          >
            <div
              class="w-[36px] h-[36px] bg-[#8C111B] rounded-full flex-shrink-0 flex items-center justify-center"
            >
              <img
                :src="item.icon"
                :alt="item.title"
                loading="lazy"
                class="w-[21px]"
              />
            </div>
            <div class="w-[102px] text-[16px] leading-[18px] text-center">
              {{ item.title }}
            </div>
          </div>
        </div>
        <div class="gradient-line"></div>
        <div
          class="pt-[14px] pb-[12px] flex items-center justify-center gap-[6px] cursor-pointer"
          @click="onOpenBenefitDrawer"
        >
          <span class="text-[18px] leading-[18px] text-[#8C111B]">{{
            authStore.i18n("cm_home.viewDetails")
          }}</span>
          <img
            loading="lazy"
            class="w-[16px] flex-shrink-0"
            :alt="authStore.i18n('cm_home.viewDetails')"
            src="@/assets/icons/home/<USER>"
          />
        </div>
      </div>
    </div>

    <!-- 购买流程 -->
    <div class="w-full mt-[12px] pt-[20px] bg-white mb-[4px]">
      <div class="w-[204px] text-[20px] leading-[22px] mx-auto">
        {{ authStore.i18n("cm_home_3stepPurchasing") }}
      </div>
      <div
        class="hidden-scrollbar flex gap-[4px] mt-[20px] overflow-x-auto pb-[18px] px-[8px]"
      >
        <div v-for="(item, index) in buySteps" :key="index">
          <img
            class="w-[140px] h-[51px] rounded-[4px] overflow-hidden"
            :src="item.image"
            :alt="item.title"
          />
          <div class="flex items-center gap-[10px] mt-[2px]">
            <div
              class="w-[24px] h-[24px] rounded-full border-1 border-[#e50113] text-[16px] leading-[16px] text-[#e50113] flex items-center justify-center"
            >
              {{ index + 1 }}
            </div>
            <div
              class="w-[96px] h-[54px] flex items-center text-[16px] leading-[18px]"
            >
              {{ item.title }}
            </div>
          </div>
        </div>
      </div>
      <div class="gradient-line"></div>
      <div
        class="pt-[14px] pb-[12px] flex items-center justify-center gap-[6px] cursor-pointer"
        @click="onOpenBuyStepsDrawer"
      >
        <span class="text-[18px] leading-[18px]">{{
          authStore.i18n("cm_home.viewDetails")
        }}</span>
        <img
          loading="lazy"
          class="w-[16px] flex-shrink-0"
          :alt="authStore.i18n('cm_home.viewDetails')"
          src="@/assets/icons/home/<USER>"
        />
      </div>
    </div>

    <!-- 客户专属活动 -->
    <div class="mt-[8px]" v-if="pageData.customerExclusiveActivities.length">
      <category-card
        data-spm-box="homepage-exclusive-activity"
        v-for="(item, index) in pageData.customerExclusiveActivities"
        :key="index"
        :cateColor="cateColorArr[index]"
        :cateInfo="item"
        class="mt-[0.16rem] first:mt-0"
      ></category-card>
    </div>

    <!-- 热销货盘 -->
    <category-card
      class="mt-[8px]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length]"
      :cateInfo="pageData.recommendPackingGoods"
    ></category-card>

    <!-- 热销货盘集合 -->
    <div class="px-[8px] py-[16px] bg-white mt-[0.16rem]">
      <div
        class="w-[260px] mx-auto text-center line-clamp-2 text-[20px] leading-[22px]"
      >
        Descubra su próxima oportunidad de negocio
      </div>
      <div class="w-full flex justify-between mt-[14px]">
        <a
          data-spm-box="homepage-banner-yiwu"
          href="/h5/selector?code=yiwu-index"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Los productos populares del Mercado de Yiwu"
            class="w-[177px] rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
        <a
          href="/selector?code=festival-index"
          data-spm-box="homepage-banner-festival"
        >
          <img
            loading="lazy"
            src="@/assets/icons/home/<USER>"
            alt="Lista de los productos de fiesta más vendidos"
            class="w-[177px] rounded-[0.08rem]"
            referrerpolicy="no-referrer"
          />
        </a>
      </div>
    </div>

    <!-- 快速搜索 -->
    <div class="relative w-full mt-[12px]">
      <img src="@/assets/icons/home/<USER>" alt="" class="w-full" />
      <div class="absolute top-0 right-0 pt-[340px] px-[20px] text-center">
        <div class="flex">
          <div class="w-[167px] flex flex-col items-center gap-[0.2rem]">
            <img
              class="w-[30px] h-[30px]"
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.quickPreciseSearch')"
            />
            <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
              {{ authStore.i18n("cm_home.quickPreciseSearch") }}
            </div>
          </div>
          <div class="w-[167px] flex flex-col items-center gap-[0.2rem]">
            <img
              class="w-[30px] h-[30px]"
              src="@/assets/icons/home/<USER>"
              :alt="authStore.i18n('cm_home.fullSpanishSupport')"
            />
            <div class="text-[0.32rem] leading-[0.48rem] text-[#11263B]">
              {{ authStore.i18n("cm_home.fullSpanishSupport") }}
            </div>
          </div>
        </div>
        <a
          href="/h5/search/looking"
          class="custom-search mt-[44px]"
          data-spm-box="button-find-homepage-mid"
        >
          <img
            class="w-[0.48rem] h-[0.48rem]"
            src="@/assets/icons/home/<USER>"
            :alt="authStore.i18n('cm_home.guidedSearchInit')"
          />
          <div class="text-[0.32rem] leading-[0.32rem] whitespace-nowrap">
            {{ authStore.i18n("cm_home.guidedSearchInit") }}
          </div>
          <img
            alt="arrow"
            class="w-[0.2rem]"
            src="@/assets/icons/common/arrow-right-white.svg"
          />
        </a>
      </div>
    </div>

    <category-card
      class="mt-[0.16rem]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length + 1]"
      :cateInfo="pageData.habitableCapsuleGoods"
    ></category-card>

    <!-- “优质供应商”商品推荐 -->
    <category-card
      class="mt-[0.16rem]"
      :cateColor="cateColorArr[pageData.customerExclusiveActivities.length + 2]"
      :cateInfo="pageData.recommendSupplierGoods"
    ></category-card>

    <!-- 美客多,开学季,义乌推荐货盘-->
    <div v-for="(bannerConfig, index) in pageData.bannerConfigs" :key="index">
      <category-card
        class="mt-[0.16rem]"
        :cateInfo="bannerConfig"
      ></category-card>
    </div>

    <!-- 分类 -->
    <div
      v-for="(categoryConfig, index) in pageData.categoryConfigs"
      :key="index"
    >
      <category-card
        class="mt-[0.16rem]"
        :cateInfo="categoryConfig"
        :cateColor="cateColorArr[index + 5]"
      ></category-card>
    </div>

    <div class="mt-[12px] bg-white">
      <div class="flex flex-col items-center mb-[20px]">
        <img
          alt="point"
          loading="lazy"
          class="w-[82px] mb-[14px]"
          src="@/assets/icons/home/<USER>"
        />
        <div class="w-[254px] mb-[8px] text-[22px] leading-[26px] text-center">
          {{ authStore.i18n("cm_home_whyGlobalBuyers") }}
        </div>
        <div class="flex items-center">
          <img
            src="@/assets/icons/common/logo.svg"
            alt="logo"
            loading="lazy"
            class="w-[116px] mr-[2px]"
          />
          <span class="text-[22px] leading-[22px]">?</span>
        </div>

        <div class="w-[30px] h-[2px] bg-[#e50113] mx-auto mt-[20px]"></div>
      </div>
      <n-carousel
        draggable
        :loop="true"
        :show-dots="false"
        :show-arrow="true"
        :space-between="20"
        ref="feedbackCarousel"
        :current-index="pageData.activatedWhyUsDataIndex"
        @update:current-index="pageData.activatedWhyUsDataIndex = $event"
        class="feedback-carousel h-[372px]"
      >
        <n-carousel-item v-for="(item, index) in whyUsData" :key="index">
          <!-- :style="`height:${item.height}`" -->
          <div
            class="w-[358px] flex flex-col pt-[20px] pb-[24px] px-[32px] relative border-1 border-[#F2F2F2] rounded-[20px]"
          >
            <img
              :src="item.icon"
              :alt="item.title"
              loading="lazy"
              class="w-[48px] h-[48px] mx-auto"
            />
            <div class="text-[18px] leading-[22px]">
              {{ item.title }}
            </div>
            <div v-if="index !== 0" class="flex flex-col gap-[12px]">
              <div
                v-for="desc in item.desc"
                :key="desc"
                class="flex gap-[10px]"
              >
                <img
                  src="@/assets/icons/home/<USER>"
                  :alt="desc"
                  class="w-[14px] h-[14px] mt-[2px]"
                />
                <span class="text-[16px] leading-[20px] text-[#7F7F7F]">{{
                  desc
                }}</span>
              </div>
            </div>
            <div v-else class="text-[16px] leading-[20px] text-[#7F7F7F]">
              <div>
                ¿Cansado de esperar respuestas manuales para cotizaciones?
              </div>
              <div class="flex gap-[10px] mt-[8px]">
                <img
                  loading="lazy"
                  class="w-[14px] h-[14px] mt-[2px]"
                  src="@/assets/icons/home/<USER>"
                  alt="¿Cansado de esperar respuestas manuales para cotizaciones?"
                />
                Nuestra plataforma actualiza precios de fábricas chinas en
                tiempo real, puede consultar, comparar y ordenar en cualquier
                momento, sin depender de agentes.
              </div>
            </div>
          </div>
        </n-carousel-item>
        <template #arrow="{ prev, next }">
          <div class="custom-arrow">
            <icon-card
              @click="prev"
              name="fe:arrow-left"
              size="0.4rem"
              :color="
                pageData.activatedWhyUsDataIndex === 0 ? '#D9D9D9' : '#e50113'
              "
            >
            </icon-card>
            <div
              class="text-[0.32rem] leading-[0.4rem] h-[0.4rem] mx-[0.2rem] mt-[0.04rem] text-[#D9D9D9]"
            >
              {{ pageData.activatedWhyUsDataIndex + 1 }}/{{ whyUsData.length }}
            </div>
            <icon-card
              @click="next"
              name="fe:arrow-right"
              size="0.4rem"
              :color="
                pageData.activatedWhyUsDataIndex === whyUsData.length - 1
                  ? '#D9D9D9'
                  : '#e50113'
              "
            >
            </icon-card>
          </div>
        </template>
      </n-carousel>
    </div>

    <!-- 注册登录 -->
    <div class="login-guide" :style="`background-image: url(${loginBg})`">
      <div class="login-guide-wrapper">
        <div class="login-title">
          {{ authStore.i18n("cm_guestHome.loginTitle") }}
        </div>
        <div class="login-desc">
          {{ authStore.i18n("cm_guestHome.loginDesc") }}
        </div>
        <a
          href="/h5/user/register?pageSource=/h5"
          data-spm-box="homepage-body-register"
        >
          <n-button color="#db2221" class="section_banner-button">
            {{ authStore.i18n("cm_common.createAccount") }}
          </n-button>
        </a>
        <div class="mt-[0.2rem] text-[0.28rem]">
          {{ authStore.i18n("cm_common.haveAccount") }}
          <a
            href="/h5/user/login?pageSource=/h5"
            data-spm-box="homepage-body-login"
          >
            <span class="text-[#e50113] text-[0.32rem]">{{
              authStore.i18n("cm_common.loginAccount")
            }}</span>
          </a>
        </div>
      </div>
    </div>

    <!-- 用户评价 -->
    <div class="user-video">
      <div class="video-title">
        {{ authStore.i18n("cm_guestHome.userVideoTitle") }}
      </div>
      <div class="video-wrapper">
        <div
          class="video-item"
          v-for="(video, index) in userVideoData"
          :key="video.id"
          @click="onOpenVideo(video, index)"
        >
          <n-image
            lazy
            preview-disabled
            :src="video.videoBg"
            class="img"
            :img-props="{ referrerpolicy: 'no-referrer' }"
          />
          <div class="video-icon">
            <icon-card
              name="mingcute:play-fill"
              size="20"
              color="#322623"
            ></icon-card>
          </div>
        </div>
      </div>
    </div>
    <!-- </div> -->

    <!-- 未登录首页 -->
    <!-- <template v-if="!userInfo?.username">
      <guest-home
        v-if="abtestMode === 'A'"
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></guest-home>
      <new-guest-home
        v-else
        :yiwuHotSaleGoods="pageData.yiwuHotSaleGoods"
        :mercadoHotSaleGoods="pageData.mercadoHotSaleGoods"
      ></new-guest-home>
    </template> -->

    <!-- 底部信息 -->
    <mobile-page-footer></mobile-page-footer>
  </div>
  <!-- 底部栏 -->
  <mobile-tab-bar :naiveBar="0" />
  <!-- 视频播放 -->
  <video-modal ref="videoModalRef"></video-modal>
  <!-- 未登录优惠券弹窗 -->
  <login-register-modal ref="loginRegisterModalRef"></login-register-modal>
  <!-- 假期通知弹框 -->
  <!-- <holiday-notice-modal></holiday-notice-modal> -->
  <!-- 福利明细抽屉 -->
  <benefit-drawer ref="benefitDrawerRef" :benefit-data="benefitBreakdown" />
  <!-- 购买流程抽屉 -->
  <buy-steps-drawer ref="buyStepsDrawerRef" :buy-steps="buySteps" />
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
import { cateColorArr } from "@/utils/constant";
import CategoryCard from "./components/CategoryCard.vue";
import LoginRegisterModal from "./components/LoginRegisterModal.vue";
import HolidayNoticeModal from "@/pages/components/HolidayNoticeModal.vue";
import BenefitDrawer from "@/pages/h5/components/BenefitDrawer.vue";
import BuyStepsDrawer from "@/pages/h5/components/BuyStepsDrawer.vue";

import backSchoolHotBanner from "@/assets/icons/backSchoolHotBanner.jpg";
import homeCarouselThousands from "@/assets/icons/home/<USER>";
import homeCarouselFactories from "@/assets/icons/home/<USER>";
import homeCarouselUnique from "@/assets/icons/home/<USER>";
import homeCarouselReward from "@/assets/icons/home/<USER>";
import homeCarouselDiscount from "@/assets/icons/home/<USER>";
// import homeCarouselLive from "@/assets/icons/home/<USER>";

import factoryDirect from "@/assets/icons/home/<USER>";
import quickQuote from "@/assets/icons/home/<USER>";
import cargoConsolidation from "@/assets/icons/home/<USER>";
import totalControl from "@/assets/icons/home/<USER>";
import hassleFreeShipping from "@/assets/icons/home/<USER>";
import selectionQuotation from "@/assets/icons/home/<USER>";
import confirmationPayment from "@/assets/icons/home/<USER>";
import qualityControlShipping from "@/assets/icons/home/<USER>";
import selfServiceOrders from "@/assets/icons/home/<USER>";
import spanishPlatform from "@/assets/icons/home/<USER>";
import flexiblePurchasing from "@/assets/icons/home/<USER>";
import remoteSelection from "@/assets/icons/home/<USER>";

import list from "@/assets/icons/home/<USER>";
import listAc from "@/assets/icons/home/<USER>";
import money from "@/assets/icons/home/<USER>";
import moneyAc from "@/assets/icons/home/<USER>";
import box from "@/assets/icons/home/<USER>";
import boxAc from "@/assets/icons/home/<USER>";
import transport from "@/assets/icons/home/<USER>";
import transportAc from "@/assets/icons/home/<USER>";

const loginBg =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/27/afb3f499-42c5-4ca4-8f0d-760e36a4b591.png";
const noticeLogo =
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/268c3fd6-85ef-4db5-8640-549d908d570b.png";

const nuxtApp = useNuxtApp();
const authStore = useAuthStore();
const config = useRuntimeConfig();
const userInfo = ref<object>({});
const abtestMode = ref<string>("");
const videoModalRef = ref<any>(null);
const benefitDrawerRef = ref<any>(null);
const buyStepsDrawerRef = ref<any>(null);
const pageData = reactive(<any>{
  activatedLinkIndex: 0,
  categoryConfigs: <any>[],
  bannerConfigs: <any>[],
  recommendPackingGoods: <any>{},
  recommendSupplierGoods: <any>{},
  habitableCapsuleGoods: <any>{},
  activatedWhyUsDataIndex: 0,
});

abtestMode.value = config.public.abtestMode as string;
userInfo.value = config.public.userInfo as object;

/**
 * 为了解决轮播图在服务端渲染到浏览器渲染时，从最后一张切换到第一张可能出现的视觉跳动问题：
 * - 服务端渲染时在尾部增加首张图片
 * - 浏览器渲染时移除尾部的重复图片
 */
const carouselData = [
  homeCarouselFactories,
  homeCarouselDiscount,
  homeCarouselUnique,
  homeCarouselReward,
  homeCarouselThousands,
  homeCarouselFactories,
];

const benefitBreakdown = [
  {
    icon: factoryDirect,
    title: "Directo desde fábrica",
    desc: "Productos a precios competitivos sin intermediarios.",
  },
  {
    icon: quickQuote,
    title: "Cotización rápida",
    desc: "Generas tu lista de pedido online y te damos precios inmediatos.",
  },
  {
    icon: cargoConsolidation,
    title: "Consolidación de carga",
    desc: "Compras a varios proveedores y enviamos todo en un mismo contenedor.",
  },
  {
    icon: totalControl,
    title: "Control total",
    desc: "Verificamos calidad y gestionamos el pago por ti.",
  },
  {
    icon: hassleFreeShipping,
    title: "Envío sin complicaciones",
    desc: "Asistimos en la logística internacional hasta tu país.",
  },
];

const buySteps = [
  {
    image: selectionQuotation,
    title: "Selección & Cotización",
    desc: [
      "Explore millones de productos, agregue al carrito",
      "Envíe su solicitud (sin pago inmediato)",
    ],
  },
  {
    image: confirmationPayment,
    title: "Confirmación & Pago",
    desc: [
      "Nuestro equipo calcula costos finales (incluye envío e impuestos)",
      "Pago seguro tras confirmación",
    ],
  },
  {
    image: qualityControlShipping,
    title: "Control de Calidad & Envío",
    desc: [
      "Inspección manual + máquina en nuestro almacén",
      "Opciones de transporte aéreo/marítimo, seguimiento en tiempo real",
      "Entrega directa en su dirección",
    ],
  },
];

const whyUsData = [
  {
    icon: selfServiceOrders,
    title: "Pedidos autogestionados 24/7, sin límites por diferencia horaria",
  },
  {
    icon: spanishPlatform,
    title: "Plataforma 100% en español, sin barreras",
    desc: [
      "Somos el único sitio mayorista chino completamente en español, con descripciones, especificaciones y soporte al cliente en su idioma.",
    ],
  },
  {
    icon: flexiblePurchasing,
    title: "Flexibilidad para todo tipo de compras",
    desc: [
      'Pequeños pedidos: "Caja mínima", más de 50,000 proveedores directos, mezcla de artículos permitida.',
      "Grandes volúmenes: Sistema de comparación inteligente, revise precios de 30 proveedores en segundos.",
      "Contenedores completos: Mezcla de categorías para ahorrar en logística, con informes de control de calidad.",
    ],
  },
  {
    icon: remoteSelection,
    title: "Selección remota, ahorro de tiempo y costos",
    desc: [
      "No necesita viajar a China, todo el proceso se gestiona en línea.",
      'Si desea visitar, organizamos itinerarios "selección + tour de fábricas + logística" para mayor eficiencia.',
    ],
  },
];

const serviceData = [
  {
    img: list,
    imgAc: listAc,
    title: authStore.i18n("cm_guestHome.chooseGoods"),
    content: [
      authStore.i18n("cm_guestHome.addGoods"),
      authStore.i18n("cm_guestHome.orderGoods"),
    ],
  },
  {
    img: money,
    imgAc: moneyAc,
    title: authStore.i18n("cm_guestHome.confirmPrice"),
    content: [
      authStore.i18n("cm_guestHome.countPrice"),
      authStore.i18n("cm_guestHome.predictPrice"),
      authStore.i18n("cm_guestHome.payPrice"),
    ],
  },
  {
    img: box,
    imgAc: boxAc,
    title: authStore.i18n("cm_guestHome.payProduct"),
    content: [
      authStore.i18n("cm_guestHome.transProduct"),
      authStore.i18n("cm_guestHome.checkProduct"),
      authStore.i18n("cm_guestHome.storageProduct"),
    ],
  },
  {
    img: transport,
    imgAc: transportAc,
    title: authStore.i18n("cm_guestHome.interLogistics"),
    content: [
      authStore.i18n("cm_guestHome.chooseLogistics"),
      authStore.i18n("cm_guestHome.trackLogistics"),
      authStore.i18n("cm_guestHome.confirmLogistics"),
    ],
  },
];

const chooseData = [
  {
    title: authStore.i18n("cm_guestHome.brandTitle"),
    desc: authStore.i18n("cm_guestHome.brandDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.teamTitle"),
    desc: authStore.i18n("cm_guestHome.teamDesc"),
  },
  {
    title: authStore.i18n("cm_guestHome.resourceTitle"),
    desc: authStore.i18n("cm_guestHome.resourceDesc"),
  },
];

const carouselChooseData = [
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/08814dad-d34d-4280-a160-31d27ab1639f.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/11/8e967d71-f3b9-40b6-9e44-dfdc419f4ac3.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/f2bfd161-86ef-4dd7-9aca-4ac31e3a59f4.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/420c46e1-42c4-4912-906b-36c1cea35c32.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/32448a13-77d4-4403-8f81-c01a84a73713.jpg",
  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/09/09/0a9a9658-364a-4ef6-b0d7-5c910b3dcc5c.jpg",
];

const userVideoData = [
  {
    id: "TROzVaB3Lr0",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/df5dd193-2fc2-48fa-b3bd-ad7707b14ff7.png",
  },
  {
    id: "Tj0nrnhxgXw",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/d4f0cffb-bfd5-44e1-b062-97ba20f9f867.png",
  },
  {
    id: "_omi5a-pHkA",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/64a4ca2d-dab2-43c0-8d30-daf71766ca00.png",
  },
  {
    id: "4FVIz0PvEcE",
    videoBg:
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/21/5c300600-9cb8-4cf7-8eb6-dd8fcdbac6d8.png",
  },
];

onBeforeMount(() => {
  // 初始化轮播图数据数组，确保首尾没有重复元素
  if (
    carouselData.length > 1 &&
    carouselData[0] === carouselData[carouselData.length - 1]
  ) {
    carouselData.pop();
  }
});

onMounted(async () => {
  authStore.setFromInviteCode(); //存储分享链接上的邀请码
});

await loadPageData();
async function loadPageData() {
  try {
    await Promise.all([onHomePageData(), onRecommendGoods()]);
  } catch (error) {
    console.error("Error loading page data:", error);
  }
}

async function onHomePageData() {
  const res: any = await useHomePageData({});
  if (res?.result?.code === 200) {
    Object.assign(pageData, res?.data);
    nuxtApp.$setResponseHeaders(pageData.seoData?.responseHeaders);
  }
}

// 推荐商品
async function onRecommendGoods() {
  const HOME_CAPSULE_PRODUCT_ID =
    useRuntimeConfig().public.HOME_CAPSULE_PRODUCT_ID;
  const res: any = await useRecommendGoodsV2({
    goodsCount: 24,
    deviceType: 2,
    siteId: window?.siteData?.siteInfo?.id,
    goodsPlugTagParams: [
      {
        tagId: HOME_CAPSULE_PRODUCT_ID,
      },
    ],
  });
  if (res?.result?.code === 200) {
    // 客户专属活动
    pageData.customerExclusiveActivities =
      res.data?.customerExclusiveActivities || [];

    pageData.recommendPackingGoods = {
      spmCode: "tag-goods-packing", // 今日特惠
      tagName: authStore.i18n("cm_home.todaySpecial"),
      tagId: res.data?.recommendPackingGoods?.tagId,
      goodsList: res.data?.recommendPackingGoods?.goodsList,
    };

    //“优质供应商”商品推荐
    pageData.recommendSupplierGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.recommendSupplierGoods?.tagId,
      tagName: authStore.i18n("cm_home.recommendSupplierGoods"),
      goodsList: res.data?.recommendSupplierGoods?.goodsList,
    };

    pageData.bannerConfigs = [
      {
        bannerUrl: backSchoolHotBanner,
        spmCode: "homepage-hot-school",
        tagId: res.data?.h5BackSchoolHotSaleGoods?.tagId,
        goodsList: res.data?.h5BackSchoolHotSaleGoods?.hotSaleGoods,
      },
    ];

    // 定义分类商品配置
    pageData.categoryConfigs = [
      {
        spmCode: "homepage-hot-camera",
        tagId: res.data?.h5CameraHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.camera"),
        goodsList: res.data?.h5CameraHotSaleGoods?.hotSaleGoods,
      },
      {
        spmCode: "homepage-hot-humidifier",
        tagId: res.data?.h5HumidifierHotSaleGoods?.tagId,
        tagName: authStore.i18n("cm_guestHome.humidifier"),
        goodsList: res.data?.h5HumidifierHotSaleGoods?.hotSaleGoods,
      },
    ];

    pageData.habitableCapsuleGoods = {
      spmCode: "homepage-tag-goods",
      tagId: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.tagId,
      tagName: authStore.i18n("cm_home.habitableCapsuleGoods"),
      goodsList: res.data?.goodsPlugTagMap[HOME_CAPSULE_PRODUCT_ID]?.goodsList,
    };
  }
}

const onLinkHover = (index: any) => {
  pageData.activatedLinkIndex = index;
};

function onOpenVideo(video: any, index: any) {
  if (videoModalRef.value) {
    window?.MyStat?.addPageEvent("play_video", `播放第${index + 1}个视频`); // 埋点
    videoModalRef.value.onOpenVideo(video);
  }
}

function onOpenBenefitDrawer() {
  benefitDrawerRef.value?.openDrawer();
}

function onOpenBuyStepsDrawer() {
  buyStepsDrawerRef.value?.openDrawer();
}
</script>
<style scoped lang="scss">
.mobile-container {
  height: 100%;
  padding-bottom: 1.88rem;
  overflow-y: auto;
  background-color: #fafafa;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-wrapper {
  display: flex;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  padding: 0.28rem 0 0.36rem 0.26667rem;
  &::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
  }
  .news-item {
    display: inline-block;
    flex: 0 0 2rem;
    height: 1.28rem;
    border-radius: 0.08rem;
    margin-right: 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.3rem 0.2rem;
    div {
      width: 1.8rem;
      height: 0.64rem;
      font-size: 0.26rem;
      font-weight: 500;
      color: #fff;
      line-height: 0.4rem;
      white-space: normal;
      display: flex;
      align-items: center;
    }
  }
}

.service-wrapper {
  width: 100%;
  background-color: #fff;
  padding: 0.6rem 0.32rem 0.6rem;
  .service-title {
    font-size: 0.48rem;
    line-height: 0.6rem;
    font-weight: 500;
    color: #222;
  }
  .service-item-wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    position: relative;
    width: 100%;
    display: flex;
    .service-item {
      cursor: pointer;
      z-index: 1;
      display: flex;
      .item-title {
        width: fit-content;
        font-size: 0.3rem;
        line-height: 0.3rem;
        padding: 0.12rem 0.28rem 0.12rem 0.24rem;
        color: #333;
        border-radius: 10rem;
        background-color: #fff;
        border: 0.02rem solid #f2f2f2;
      }
    }
    .service-item-enter:not(:last-of-type) {
      margin-bottom: 0.08rem;
    }
    .service-item:not(:last-of-type) {
      margin-bottom: 0.2rem;
    }
    .service-item:not(:first-child) {
      margin-top: 0.2rem;
    }
    .item-circle {
      margin: 0.28rem 0.14rem 0;
      width: 0.12rem;
      height: 0.12rem;
      border: 0.02rem solid #e50113;
      border-radius: 100%;
      flex-shrink: 0;
      position: relative;
      background-color: #fff;
      z-index: 2;
    }
    .service-item-enter .item-circle {
      background-color: #e50113;
    }

    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
    }
    .item-desc {
      color: #767676;
      display: none;
      font-size: 0.24rem;
      // height: fit-content;
      line-height: 0.24rem;
      margin-top: 0.24rem;
    }
    .service-item-enter .item-title {
      color: #51200b;
      margin-top: -0.1rem;
      color: #fff;
      background-color: #e50113;
      border: 0.02rem solid #e50113;
    }

    .service-item-enter .item-desc {
      display: -webkit-box;
    }
    .item-tail {
      background-color: #f2f2f2;
      height: calc(100% - 0.78rem);
      left: 0.1898rem;
      top: 0.3rem;
      position: absolute;
      width: 0.02rem;
    }
  }
  .service-item-enter:last-of-type ~ .item-tail {
    height: calc(100% - 1.9rem);
  }
}

.full-link {
  width: 100%;
  color: #222;
  background-color: #fff;
  padding: 0.6rem 0.32rem;
  .full-title {
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
    color: #222;
    margin-bottom: 0.3rem;
    text-align: center;
  }

  .full-link_title {
    -webkit-box-orient: vertical;
    color: #222;
    display: -webkit-box;
    letter-spacing: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 0.36rem;
    line-height: 0.48rem;
    font-weight: 500;
  }
  .full-link_item_wrapper {
    -webkit-flex-direction: column;
    flex-direction: column;
    position: relative;
    width: 100%;
    display: flex;
    margin-bottom: 0.32rem;
    .full-link_item {
      cursor: pointer;
      z-index: 1;
      display: flex;
    }
    .full-link_item:not(:last-of-type) {
      margin-bottom: 0.2rem;
    }
    .full-link_item:not(:first-child) {
      margin-top: 0.2rem;
    }
    .full-link_item_title {
      font-size: 0.28rem;
      line-height: 0.48rem;
      font-weight: 500;
    }
    .full-link_item_desc {
      color: #767676;
      font-size: 0.24rem;
      height: fit-content;
      line-height: 0.4rem;
      margin-top: 0.12rem;
      -webkit-line-clamp: 4;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .full-link_icon {
      width: 0.4rem;
      height: 0.4rem;
      margin-inline-end: 0.2rem;
      position: relative;
      flex-shrink: 0;
      background-color: #e50113;
      border-radius: 50%;
      margin-top: 0.2rem;
      flex-shrink: 0;
    }
    .full-link_item_tail {
      background-color: #3a180b;
      height: 104%;
      left: 0.18rem;
      position: absolute;
      width: 0.04rem;
      border-radius: 0.04rem;
      top: -0.12rem;
    }
  }
}

.login-guide {
  width: 100%;
  background-color: #473229;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow: hidden;
  text-align: center;
  width: 100%;
  position: relative;
  .login-guide-wrapper {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.6rem 0.32rem;
    color: #000;
    z-index: 2;
  }
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }

  .login-title {
    font-size: 0.4rem;
    font-weight: 500;
    line-height: 0.6rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .login-desc {
    font-size: 0.28rem;
    line-height: 0.4rem;
    margin-top: 0.2rem;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
  }
  .section_banner-button {
    width: 3.8rem;
    font-size: 0.32rem;
    line-height: 0.7rem;
    padding: 0.2rem 0.34rem;
    border-radius: 0.4rem;
    margin-top: 0.36rem;
    box-shadow: 0.06rem 0.06rem 0.1rem rgba(0, 0, 0, 0.3);
  }
}

.user-video {
  width: 100%;
  padding: 0.6rem 0.32rem;

  .video-title {
    font-size: 0.36rem;
    font-weight: 500;
    line-height: 0.6rem;
    text-align: center;
  }
  .video-wrapper {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    &::-webkit-scrollbar {
      display: none; /* 隐藏滚动条 */
    }
  }
  .video-item {
    flex: 0 0 2.8rem;
    width: 2.8rem;
    margin: 0.2rem 0.2rem 0.2rem 0;
    position: relative;
    cursor: pointer;
    .img {
      width: 100%;
      border-radius: 0.24rem;
    }
    .video-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #fff;
      width: 0.6rem;
      height: 0.6rem;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
:deep(.n-carousel .n-carousel__slides .n-carousel__slide) {
  text-align: center;
}

:deep(.home-header-carousel.n-carousel .n-carousel__dots) {
  bottom: 0.32rem;
}

.custom-search {
  display: inline-flex;
  padding: 0.34rem 0.48rem;
  align-items: center;
  gap: 0.16rem;
  border-radius: 10rem;
  background: #11263b;
  color: #fff;
  transition: background 0.3s ease;
  &:hover {
    background: #0f2e4d;
  }
}

:deep(.logo-header) {
  padding-top: 0px !important;
}
.benefit-breakdown {
  box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.05);
}
.hidden-scrollbar {
  scroll-behavior: smooth;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

.gradient-line {
  width: 100%;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(242, 242, 242, 0.2) 0%,
    #f2f2f2 50%,
    rgba(242, 242, 242, 0.2) 100%
  );
}

.feedback-carousel {
  .custom-arrow {
    display: flex;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    margin: 0 auto;
    // transform: translateX(-50%);
    z-index: 10;
  }
}
</style>
