<template>
  <!-- 福利明细抽屉 -->
  <n-drawer
    v-model:show="pageData.dialogVisible"
    width="100%"
    placement="bottom"
    :trap-focus="false"
    default-height="88%"
  >
    <n-drawer-content closable>
      <template #header>
        <div
          class="w-[212px] mx-auto text-[20px] leading-[22px] text-center font-normal"
        >
          {{ authStore.i18n("cm_home_3stepPurchasing") }}
        </div>
      </template>

      <div class="pt-[21px] pb-[30px] px-[20px]">
        <div
          v-for="(step, stepIndex) in props.buySteps"
          :key="stepIndex"
          class="mb-[31px] last:mb-0"
        >
          <div class="flex gap-[16px]">
            <div
              class="flex flex-col items-center bg-[#FAFAFA] rounded-[8px] w-[108px] pb-[8px]"
              :class="
                stepIndex === props.buySteps.length - 1
                  ? 'gap-[35px]'
                  : 'gap-[5px]'
              "
            >
              <!-- 圆形数字 -->
              <div
                class="w-[28px] h-[28px] rounded-full border-1 border-[#E50113] bg-white flex items-center justify-center mb-[7px] text-[20px] leading-[20px] text-[#e50113] mt-[-13px]"
              >
                {{ stepIndex + 1 }}
              </div>

              <div
                class="w-[96px] h-[54px] flex items-center text-[16px] leading-[18px] text-[#333] mt-[2px] text-center"
              >
                {{ step.title }}
              </div>

              <!-- 步骤图片 -->
              <img
                :src="step.image"
                :alt="step.title"
                loading="lazy"
                class="w-[96px] h-[38px] rounded-[4px] overflow-hidden"
              />
            </div>

            <div class="flex-1 pt-[8px]">
              <div
                v-for="(desc, descIndex) in step.desc"
                :key="descIndex"
                class="flex items-start gap-[12px] last:mb-0"
                :class="
                  stepIndex === props.buySteps.length - 1
                    ? 'mb-[8px]'
                    : 'mb-[16px]'
                "
              >
                <img
                  src="/assets/icons/home/<USER>"
                  alt="check"
                  loading="lazy"
                  class="w-[12px] h-[12px] flex-shrink-0 mt-[4px]"
                />
                <span class="text-[16px] leading-[18px] text-[#7F7F7F] flex-1">
                  {{ desc }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </n-drawer-content>
  </n-drawer>
</template>

<script setup lang="ts">
import { useAuthStore } from "@/stores/authStore";
const authStore = useAuthStore();

const props = defineProps({
  buySteps: {
    type: Array,
    default: () => [],
  },
});

const pageData = reactive({
  dialogVisible: false,
});

const openDrawer = () => {
  pageData.dialogVisible = true;
};

const closeDrawer = () => {
  pageData.dialogVisible = false;
};

defineExpose({
  openDrawer,
  closeDrawer,
});
</script>

<style lang="scss" scoped>
:deep(.n-drawer-body-content-wrapper) {
  padding: 0 !important;
}
:deep(.n-drawer-header) {
  padding: 12px 13px !important;
}
:deep(.n-base-icon svg) {
  width: 16px !important;
  height: 16px !important;
}
:deep(.n-base-close) {
  color: #333 !important;
}
</style>
